import { Injectable, Logger } from '@nestjs/common';
import { ObjectiveRepository } from '../repositories/objective.repository';
import { OkrCycleRepository } from '../repositories/okr-cycle.repository';
import { KeyResultRepository } from '../repositories/key-result.repository';
import { Objective } from '../entities/objective.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common';
import { OKRS_ERROR_CODES } from '../errors/okrs-error.code';
import { CreateObjectiveDto } from '../dto/objective/create-objective.dto';
import { ObjectiveResponseDto } from '../dto/objective/objective-response.dto';
import { ObjectiveQueryDto } from '../dto/objective/objective-query.dto';
import { UpdateObjectiveDto } from '../dto/objective/update-objective.dto';
import { BulkDeleteObjectiveDto } from '../dto/objective/bulk-delete-objective.dto';
import { BulkDeleteObjectiveResponseDto } from '../dto/objective/bulk-delete-objective-response.dto';

/**
 * Service for objectives
 */
@Injectable()
export class ObjectiveService {
  private readonly logger = new Logger(ObjectiveService.name);

  constructor(
    private readonly objectiveRepository: ObjectiveRepository,
    private readonly okrCycleRepository: OkrCycleRepository,
    private readonly keyResultRepository: KeyResultRepository,
  ) {}

  /**
   * Create a new objective
   * @param tenantId Tenant ID
   * @param userId User ID creating the objective
   * @param dto Create objective DTO
   * @returns Created objective response
   */
  async create(
    tenantId: number,
    userId: number,
    dto: CreateObjectiveDto,
  ): Promise<ObjectiveResponseDto> {
    // Validate cycle exists
    const cycle = await this.okrCycleRepository.findById(tenantId, dto.cycleId);
    if (!cycle) {
      throw new AppException(
        OKRS_ERROR_CODES.OBJECTIVE_INVALID_CYCLE,
        `Không tìm thấy chu kỳ OKR với ID ${dto.cycleId}`,
      );
    }

    // Xác định ownerId dựa trên type
    let finalOwnerId: number;

    switch (dto.type) {
      case 'INDIVIDUAL':
        // Với INDIVIDUAL, ownerId luôn là user đang đăng nhập
        finalOwnerId = userId;
        break;

      case 'DEPARTMENT':
      case 'COMPANY':
        // Với DEPARTMENT và COMPANY, phải có ownerId từ frontend
        if (!dto.ownerId) {
          throw new AppException(
            OKRS_ERROR_CODES.OBJECTIVE_INVALID_OWNER,
            `Loại mục tiêu ${dto.type} yêu cầu phải có ownerId`,
          );
        }
        finalOwnerId = dto.ownerId;
        break;

      default:
        throw new AppException(
          OKRS_ERROR_CODES.OBJECTIVE_INVALID_TYPE,
          `Loại mục tiêu không hợp lệ: ${dto.type}`,
        );
    }

    // Chuẩn bị dữ liệu tạo mới
    const createData: Partial<Objective> = {
      title: dto.title,
      description: dto.description,
      ownerId: finalOwnerId,
      departmentId: dto.departmentId,
      cycleId: dto.cycleId,
      type: dto.type,
      createdBy: userId,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      progress: 0,
      status: 'active',
    };

    // Create objective
    const objective = await this.objectiveRepository.create(
      tenantId,
      createData,
    );

    // Lấy lại objective với parent title nếu có
    const objectiveWithParent = await this.objectiveRepository.findById(
      tenantId,
      objective.id,
    );

    return this.mapToResponseDto(objectiveWithParent || objective);
  }

  /**
   * Get all objectives with pagination and filtering
   * @param query Query parameters
   * @returns Paginated list of objective responses
   */
  async findAll(
    tenantId: number,
    query: ObjectiveQueryDto,
  ): Promise<PaginatedResult<ObjectiveResponseDto>> {
    console.log(`[ObjectiveService.findAll] Called with tenantId: ${tenantId}`);

    const result = await this.objectiveRepository.findAll(tenantId, query);

    return {
      items: result.items.map((objective) => this.mapToResponseDto(objective)),
      meta: result.meta,
    };
  }

  /**
   * Get an objective by ID
   * @param tenantId Tenant ID
   * @param id Objective ID
   * @returns Objective response
   */
  async findById(tenantId: number, id: number): Promise<ObjectiveResponseDto> {
    const objective = await this.objectiveRepository.findById(tenantId, id);

    if (!objective) {
      throw new AppException(
        OKRS_ERROR_CODES.OBJECTIVE_NOT_FOUND,
        `Không tìm thấy mục tiêu với ID ${id}`,
      );
    }

    return this.mapToResponseDto(objective);
  }

  /**
   * Update an objective
   * @param tenantId Tenant ID
   * @param id Objective ID
   * @param userId User ID updating the objective
   * @param dto Update objective DTO
   * @returns Updated objective response
   */
  async update(
    tenantId: number,
    id: number,
    userId: number,
    dto: UpdateObjectiveDto,
  ): Promise<ObjectiveResponseDto> {
    // Check if objective exists
    const existingObjective = await this.objectiveRepository.findById(
      tenantId,
      id,
    );

    if (!existingObjective) {
      throw new AppException(
        OKRS_ERROR_CODES.OBJECTIVE_NOT_FOUND,
        `Không tìm thấy mục tiêu với ID ${id}`,
      );
    }

    // Validate date range if both dates are provided
    if (dto.startDate && dto.endDate) {
      if (new Date(dto.startDate) > new Date(dto.endDate)) {
        throw new AppException(
          OKRS_ERROR_CODES.OBJECTIVE_INVALID_DATE_RANGE,
          'Ngày bắt đầu phải trước ngày kết thúc',
        );
      }
    } else if (dto.startDate && !dto.endDate) {
    
    } else if (!dto.startDate && dto.endDate) {
    }

    // Validate parent objective exists if provided
    if (dto.parentId) {
      // Check for self-reference
      if (dto.parentId === id) {
        throw new AppException(
          OKRS_ERROR_CODES.OBJECTIVE_INVALID_PARENT,
          'Mục tiêu không thể là mục tiêu cha của chính nó',
        );
      }

      const parentObjective = await this.objectiveRepository.findById(
        tenantId,
        dto.parentId,
      );
      if (!parentObjective) {
        throw new AppException(
          OKRS_ERROR_CODES.OBJECTIVE_INVALID_PARENT,
          `Không tìm thấy mục tiêu cha với ID ${dto.parentId}`,
        );
      }
    }

    // Xác định ownerId dựa trên type nếu có thay đổi type
    let finalOwnerId = dto.ownerId; // Giữ nguyên nếu không có thay đổi

    if (dto.type) {
      switch (dto.type) {
        case 'INDIVIDUAL':
          // Với INDIVIDUAL, ownerId luôn là user đang đăng nhập
          finalOwnerId = userId;
          break;

        case 'DEPARTMENT':
        case 'COMPANY':
          // Với DEPARTMENT và COMPANY, phải có ownerId từ frontend
          if (dto.ownerId === undefined) {
            throw new AppException(
              OKRS_ERROR_CODES.OBJECTIVE_INVALID_OWNER,
              `Loại mục tiêu ${dto.type} yêu cầu phải có ownerId`,
            );
          }
          finalOwnerId = dto.ownerId;
          break;

        default:
          throw new AppException(
            OKRS_ERROR_CODES.OBJECTIVE_INVALID_TYPE,
            `Loại mục tiêu không hợp lệ: ${dto.type}`,
          );
      }
    }

    // Chuẩn bị dữ liệu cập nhật
    const updateData: Partial<Objective> = {
      title: dto.title,
      description: dto.description,
      ownerId: finalOwnerId,
      departmentId: dto.departmentId,
      status: dto.status,
      type: dto.type,
      updatedAt: Date.now(),
    };

    // Update objective
    const updatedObjective = await this.objectiveRepository.update(
      tenantId,
      id,
      updateData,
    );

    if (!updatedObjective) {
      throw new AppException(
        OKRS_ERROR_CODES.OBJECTIVE_NOT_FOUND,
        `Không tìm thấy mục tiêu với ID ${id}`,
      );
    }

    return this.mapToResponseDto(updatedObjective);
  }

  /**
   * Delete an objective
   * @param tenantId Tenant ID
   * @param id Objective ID
   * @returns True if deleted
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    // Check if objective exists
    const existingObjective = await this.objectiveRepository.findById(
      tenantId,
      id,
    );

    if (!existingObjective) {
      throw new AppException(
        OKRS_ERROR_CODES.OBJECTIVE_NOT_FOUND,
        `Không tìm thấy mục tiêu với ID ${id}`,
      );
    }

    // Delete objective
    const deleted = await this.objectiveRepository.delete(tenantId, id);

    if (!deleted) {
      throw new AppException(
        OKRS_ERROR_CODES.OBJECTIVE_NOT_FOUND,
        `Không tìm thấy mục tiêu với ID ${id}`,
      );
    }

    return true;
  }

  /**
   * Update objective progress
   * @param tenantId Tenant ID
   * @param id Objective ID
   * @param progress Progress value (0-100)
   * @returns Updated objective response
   */
  async updateProgress(
    tenantId: number,
    id: number,
    progress: number,
  ): Promise<ObjectiveResponseDto> {
    // Check if objective exists
    const existingObjective = await this.objectiveRepository.findById(
      tenantId,
      id,
    );

    if (!existingObjective) {
      throw new AppException(
        OKRS_ERROR_CODES.OBJECTIVE_NOT_FOUND,
        `Không tìm thấy mục tiêu với ID ${id}`,
      );
    }

    // Validate progress value
    if (progress < 0 || progress > 100) {
      throw new AppException(
        OKRS_ERROR_CODES.OBJECTIVE_INVALID_OWNER, // Sử dụng mã lỗi khác vì OBJECTIVE_INVALID_PROGRESS không tồn tại
        'Giá trị tiến độ phải từ 0 đến 100',
      );
    }

    // Update objective progress
    const updatedObjective = await this.objectiveRepository.updateProgress(
      tenantId,
      id,
      progress,
    );

    if (!updatedObjective) {
      throw new AppException(
        OKRS_ERROR_CODES.OBJECTIVE_NOT_FOUND,
        `Không tìm thấy mục tiêu với ID ${id}`,
      );
    }

    return this.mapToResponseDto(updatedObjective);
  }

  /**
   * Map objective entity to response DTO
   * @param objective Objective entity (có thể có parentTitle từ JOIN)
   * @returns Objective response DTO
   */
  private mapToResponseDto(objective: Objective & { parentTitle?: string }): ObjectiveResponseDto {
    const response = new ObjectiveResponseDto();

    response.id = objective.id;
    response.title = objective.title;
    response.description = objective.description;
    response.ownerId = objective.ownerId;
    response.departmentId = objective.departmentId;
    response.parentTitle = objective.parentTitle || null;
    response.cycleId = objective.cycleId;
    response.type = objective.type;
    response.progress = objective.progress;
    response.status = objective.status;

    response.createdBy = objective.createdBy;
    response.createdAt = objective.createdAt;
    response.updatedAt = objective.updatedAt;

    return response;
  }

  /**
   * Xóa nhiều mục tiêu và tất cả key-result liên quan
   * @param tenantId Tenant ID
   * @param dto DTO chứa danh sách ID mục tiêu cần xóa
   * @returns Kết quả xóa nhiều mục tiêu
   */
  async bulkDelete(
    tenantId: number,
    dto: BulkDeleteObjectiveDto,
  ): Promise<BulkDeleteObjectiveResponseDto> {
    const { ids } = dto;

    // Tìm tất cả mục tiêu theo danh sách ID
    const objectives = await this.objectiveRepository.findByIds(tenantId, ids);
    const foundIds = objectives.map((obj) => obj.id);
    const notFoundIds = ids.filter((id) => !foundIds.includes(id));

    const result: BulkDeleteObjectiveResponseDto = {
      totalRequested: ids.length,
      successCount: 0,
      failureCount: 0,
      deletedObjectiveIds: [],
      deletedKeyResultsCount: 0,
      failures: [],
    };

    // Thêm các ID không tìm thấy vào danh sách lỗi
    notFoundIds.forEach((id) => {
      result.failures.push({
        id,
        reason: `Không tìm thấy mục tiêu với ID ${id}`,
      });
    });

    // Các mục tiêu có thể xóa (tất cả mục tiêu tìm thấy đều có thể xóa)
    const deletableIds = foundIds;

    if (deletableIds.length > 0) {
      try {
        // Xóa tất cả key-result liên quan trước
        const deletedKeyResultsCount =
          await this.keyResultRepository.deleteByObjectiveIds(
            tenantId,
            deletableIds,
          );

        // Xóa các mục tiêu
        const deletedObjectivesCount =
          await this.objectiveRepository.bulkDelete(tenantId, deletableIds);

        result.successCount = deletedObjectivesCount;
        result.deletedObjectiveIds = deletableIds.slice(
          0,
          deletedObjectivesCount,
        );
        result.deletedKeyResultsCount = deletedKeyResultsCount;

        this.logger.log(
          `Bulk delete objectives: ${deletedObjectivesCount} mục tiêu và ${deletedKeyResultsCount} key-result đã được xóa`,
        );
      } catch (error) {
        this.logger.error('Lỗi khi xóa nhiều mục tiêu:', error);

        // Thêm tất cả ID vào danh sách lỗi nếu có lỗi xảy ra
        deletableIds.forEach((id) => {
          result.failures.push({
            id,
            reason: 'Lỗi hệ thống khi xóa mục tiêu',
          });
        });
      }
    }

    result.failureCount = result.failures.length;

    return result;
  }
}
